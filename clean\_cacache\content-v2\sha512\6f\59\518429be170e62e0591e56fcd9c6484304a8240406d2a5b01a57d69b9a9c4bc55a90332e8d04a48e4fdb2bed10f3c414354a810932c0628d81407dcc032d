{"_attachments": {}, "_id": "fsevents", "_rev": "3174-61f14b5f963ca28f5ee48597", "description": "Native Access to MacOS FSEvents", "dist-tags": {"latest": "2.3.3", "nan": "1.2.13"}, "license": "MIT", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "name": "fsevents", "readme": "# fsevents\n\nNative access to MacOS FSEvents in [Node.js](https://nodejs.org/)\n\nThe FSEvents API in MacOS allows applications to register for notifications of\nchanges to a given directory tree. It is a very fast and lightweight alternative\nto kqueue.\n\nThis is a low-level library. For a cross-platform file watching module that\nuses fsevents, check out [<PERSON><PERSON><PERSON>](https://github.com/paulmillr/chokidar).\n\n## Usage\n\n```sh\nnpm install fsevents\n```\n\nSupports only **Node.js v8.16 and higher**.\n\n```js\nconst fsevents = require('fsevents');\n\n// To start observation\nconst stop = fsevents.watch(__dirname, (path, flags, id) => {\n  const info = fsevents.getInfo(path, flags);\n});\n\n// To end observation\nstop();\n```\n\n> **Important note:** The API behaviour is slightly different from typical JS APIs. The `stop` function **must** be\n> retrieved and stored somewhere, even if you don't plan to stop the watcher. If you forget it, the garbage collector\n> will eventually kick in, the watcher will be unregistered, and your callbacks won't be called anymore.\n\nThe callback passed as the second parameter to `.watch` get's called whenever the operating system detects a\na change in the file system. It takes three arguments:\n\n###### `fsevents.watch(dirname: string, (path: string, flags: number, id: string) => void): () => Promise<undefined>`\n\n * `path: string` - the item in the filesystem that have been changed\n * `flags: number` - a numeric value describing what the change was\n * `id: string` - an unique-id identifying this specific event\n\n Returns closer callback which when called returns a Promise resolving when the watcher process has been shut down.\n\n###### `fsevents.getInfo(path: string, flags: number, id: string): FsEventInfo`\n\nThe `getInfo` function takes the `path`, `flags` and `id` arguments and converts those parameters into a structure\nthat is easier to digest to determine what the change was.\n\nThe `FsEventsInfo` has the following shape:\n\n```js\n/**\n * @typedef {'created'|'modified'|'deleted'|'moved'|'root-changed'|'cloned'|'unknown'} FsEventsEvent\n * @typedef {'file'|'directory'|'symlink'} FsEventsType\n */\n{\n  \"event\": \"created\", // {FsEventsEvent}\n  \"path\": \"file.txt\",\n  \"type\": \"file\",    // {FsEventsType}\n  \"changes\": {\n    \"inode\": true,   // Had iNode Meta-Information changed\n    \"finder\": false, // Had Finder Meta-Data changed\n    \"access\": false, // Had access permissions changed\n    \"xattrs\": false  // Had xAttributes changed\n  },\n  \"flags\": 0x100000000\n}\n```\n\n## Changelog\n\n- v2.3 supports Apple Silicon ARM CPUs\n- v2 supports node 8.16+ and reduces package size massively\n- v1.2.8 supports node 6+\n- v1.2.7 supports node 4+\n\n## Troubleshooting\n\n- I'm getting `EBADPLATFORM` `Unsupported platform for fsevents` error.\n- It's fine, nothing is broken. fsevents is macos-only. Other platforms are skipped. If you want to hide this warning, report a bug to NPM bugtracker asking them to hide ebadplatform warnings by default.\n\n## License\n\nThe MIT License Copyright (C) 2010-2020 by Philipp Dunkel, Ben Noordhuis, Elan Shankar, Paul Miller — see LICENSE file.\n\nVisit our [GitHub page](https://github.com/fsevents/fsevents) and [NPM Page](https://npmjs.org/package/fsevents)\n", "time": {"created": "2022-01-26T13:23:43.421Z", "modified": "2025-06-19T20:46:12.485Z", "2.3.2": "2021-02-05T14:46:33.925Z", "2.3.1": "2021-01-05T15:23:19.751Z", "2.3.0": "2021-01-05T11:06:52.663Z", "2.2.2": "2021-01-05T02:47:04.296Z", "2.2.1": "2020-11-05T16:51:19.929Z", "2.2.0": "2020-11-03T12:58:19.820Z", "1.2.13": "2020-05-05T21:06:30.384Z", "2.1.3": "2020-04-22T08:48:23.251Z", "1.2.12": "2020-03-19T09:14:58.168Z", "1.2.11": "2019-12-14T11:28:18.959Z", "1.2.10": "2019-12-13T11:25:04.547Z", "2.1.2": "2019-11-09T17:08:30.550Z", "2.1.1": "2019-10-14T00:14:41.467Z", "2.1.0": "2019-10-01T00:52:09.822Z", "2.0.7": "2019-05-16T13:07:15.212Z", "1.2.9": "2019-04-29T16:17:22.547Z", "2.0.6": "2019-04-16T19:19:31.595Z", "2.0.5": "2019-04-16T16:50:24.835Z", "1.2.8": "2019-04-16T16:45:14.068Z", "2.0.4": "2019-04-16T15:19:08.646Z", "2.0.3": "2019-04-01T13:51:03.216Z", "1.2.7": "2019-01-18T15:54:27.775Z", "1.2.6": "2019-01-15T19:22:35.178Z", "2.0.2-pre-1": "2018-12-29T10:57:04.076Z", "2.0.1": "2018-11-10T14:25:27.802Z", "2.0.0": "2018-11-10T13:45:29.438Z", "1.2.4": "2018-05-15T14:49:30.090Z", "1.2.3": "2018-04-27T17:30:08.673Z", "1.2.2": "2018-04-23T15:58:15.735Z", "1.2.0": "2018-04-20T14:59:24.344Z", "1.1.3": "2017-11-10T22:23:22.311Z", "1.1.2": "2017-06-13T04:40:40.543Z", "1.1.1": "2017-02-20T05:18:05.877Z", "1.1.0": "2017-02-19T17:47:36.660Z", "1.0.18-0": "2017-02-19T16:55:47.760Z", "1.0.17": "2017-01-04T22:25:53.376Z", "1.0.15": "2016-11-02T23:38:09.421Z", "1.0.14": "2016-07-18T12:56:52.080Z", "1.0.12": "2016-04-29T14:34:06.131Z", "1.0.11": "2016-03-31T19:18:02.557Z", "1.0.10": "2016-03-31T18:16:24.110Z", "1.0.9": "2016-03-23T20:30:48.264Z", "1.0.8": "2016-02-23T10:24:36.327Z", "1.0.7": "2016-02-03T15:12:36.865Z", "1.0.6": "2015-12-10T14:02:06.618Z", "1.0.5": "2015-11-05T00:03:45.065Z", "1.0.4": "2015-11-04T17:44:36.966Z", "1.0.3": "2015-11-03T17:42:34.374Z", "1.0.2": "2015-10-06T16:54:19.342Z", "1.0.1": "2015-09-29T13:33:22.418Z", "1.0.0": "2015-09-09T16:28:53.632Z", "0.3.8": "2015-08-06T20:53:40.505Z", "0.3.7": "2015-07-30T21:22:55.082Z", "0.3.6": "2015-05-05T11:40:56.957Z", "0.3.5": "2015-01-27T17:33:56.632Z", "0.3.4": "2015-01-15T18:47:56.914Z", "0.3.3": "2015-01-15T18:25:40.249Z", "0.3.2": "2015-01-15T18:16:40.178Z", "0.3.1": "2014-11-04T23:57:07.078Z", "0.3.0": "2014-08-19T22:00:57.872Z", "0.2.0": "2014-01-31T22:45:31.014Z", "0.1.6": "2013-11-28T11:01:53.138Z", "0.1.5": "2013-07-08T12:58:59.106Z", "0.1.4": "2013-07-06T22:47:07.832Z", "0.1.3": "2013-07-06T22:26:35.459Z", "0.1.1": "2013-07-06T22:04:02.366Z", "2.3.3": "2023-08-21T16:24:22.854Z"}, "versions": {"2.3.2": {"name": "fsevents", "version": "2.3.2", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "a7f5d00939b74e141a73131468c4ce48ee0f2197", "_id": "fsevents@2.3.2", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "dist": {"shasum": "8a526f78b8fdf4623b709e0b975c52c24c02fd1a", "size": 22066, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA=="}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.3.2_1612536393763_0.6823972486617933"}, "_hasShrinkwrap": false, "publish_time": 1612536393925, "_cnpm_publish_time": 1612536393925, "_cnpmcore_publish_time": "2021-12-13T10:54:53.286Z", "hasInstallScript": true}, "2.3.1": {"name": "fsevents", "version": "2.3.1", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "328ae396700969fd8345f13cc4fb88c495517cd9", "_id": "fsevents@2.3.1", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.8", "dist": {"shasum": "b209ab14c61012636c8863507edf7fb68cc54e9f", "size": 23774, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.1.tgz", "integrity": "sha512-YR47Eg4hChJGAB1O3yEAOkGO+rlzutoICGqGo9EZ4lKWokzZRSyIW1QmTzqjtw8MJdj9srP869CuWw/hyzSiBw=="}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.3.1_1609860199626_0.7084527693026021"}, "_hasShrinkwrap": false, "publish_time": 1609860199751, "_cnpm_publish_time": 1609860199751, "_cnpmcore_publish_time": "2021-12-13T10:54:53.611Z", "hasInstallScript": true}, "2.3.0": {"name": "fsevents", "version": "2.3.0", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "70da1984bd0919b6e2f208bb6da2f40075411b8e", "_id": "fsevents@2.3.0", "_nodeVersion": "15.5.0", "_npmVersion": "7.3.0", "dist": {"shasum": "ed42dc76d22943057d58a3856ce3885c0f982948", "size": 23795, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.0.tgz", "integrity": "sha512-SmvpCnCbknBKrMhodrIdwGBhn8kAEp8VUyrcH13clA5cq1C5fyN2+b3lQBdjpO1X/yJrTNySaggyYA62h+28ug=="}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.3.0_1609844812519_0.5239945270225788"}, "_hasShrinkwrap": false, "publish_time": 1609844812663, "_cnpm_publish_time": 1609844812663, "_cnpmcore_publish_time": "2021-12-13T10:54:53.946Z", "hasInstallScript": true}, "2.2.2": {"name": "fsevents", "version": "2.2.2", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "414f665be3322cd6bfa9f8b8e4e28008dda67aa3", "_id": "fsevents@2.2.2", "_nodeVersion": "12.20.0", "_npmVersion": "6.14.8", "dist": {"shasum": "ae94eddce6e1cbc4fd0bd2d43080c5406abd5d6b", "size": 23761, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.2.2.tgz", "integrity": "sha512-xJEn9cTLpaYpLA/ACD3wjQRA7unhS0b7veAzx6F8cVhi8yjwjAPzHsVibMIAWwwMVHixe6WqoFXlc36FsNM80w=="}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.2.2_1609814824127_0.351756155406834"}, "_hasShrinkwrap": false, "publish_time": 1609814824296, "_cnpm_publish_time": 1609814824296, "_cnpmcore_publish_time": "2021-12-13T10:54:54.286Z", "hasInstallScript": true}, "2.2.1": {"name": "fsevents", "version": "2.2.1", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "f650d829f63e546b4afda6091fcdf4acb7e1e59b", "_id": "fsevents@2.2.1", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "1fb02ded2036a8ac288d507a65962bd87b97628d", "size": 13518, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.2.1.tgz", "integrity": "sha512-bTLYHSeC0UH/EFXS9KqWnXuOl/wHK5Z/d+ghd5AsFMYN7wIGkUCOJyzy88+wJKkZPGON8u4Z9f6U4FdgURE9qA=="}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.2.1_1604595079775_0.5023311655548972"}, "_hasShrinkwrap": false, "publish_time": 1604595079929, "_cnpm_publish_time": 1604595079929, "_cnpmcore_publish_time": "2021-12-13T10:54:54.587Z", "hasInstallScript": true}, "2.2.0": {"name": "fsevents", "version": "2.2.0", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "readmeFilename": "README.md", "gitHead": "6a3bb59898ae2da68bed80afdc2ab59583972168", "_id": "fsevents@2.2.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"shasum": "4150396a427ecb5baa747725b70270a72b17bc17", "size": 13478, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.2.0.tgz", "integrity": "sha512-pKnaUh2TNvk+/egJdBw1h46LwyLx8BzEq+MGCf/RMCVfEHHsGOCWG00dqk91kUPPArIIwMBg9T/virxwzP03cA=="}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.2.0_1604408299683_0.27155820439605205"}, "_hasShrinkwrap": false, "publish_time": 1604408299820, "_cnpm_publish_time": 1604408299820, "_cnpmcore_publish_time": "2021-12-13T10:54:55.004Z", "hasInstallScript": true}, "1.2.13": {"name": "fsevents", "version": "1.2.13", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "os": ["darwin"], "engines": {"node": ">= 4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node install.js"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "readmeFilename": "Readme.md", "gitHead": "844a05d41fa0f764c7457dae266b0ab50384394a", "_id": "fsevents@1.2.13", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "dist": {"shasum": "f325cb0455592428bcf11b383370ef70e3bfcc38", "size": 7859, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha512-oWb1Z6mkHIskLzEJ/XWX0srkpkTQ7vaopMQkyaEIoq0fmtFVxOthb8cCxeT+p3ynTdkk/RZwbgG4brR5BeWECw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.13_1588712790211_0.34870418787981894"}, "_hasShrinkwrap": false, "publish_time": 1588712790384, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "_cnpm_publish_time": 1588712790384, "_cnpmcore_publish_time": "2021-12-13T10:54:55.376Z", "hasInstallScript": true}, "2.1.3": {"name": "fsevents", "version": "2.1.3", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^6.1.0"}, "gypfile": true, "gitHead": "864d79b4df9d776663d389062ea5fd9324276ff6", "_id": "fsevents@2.1.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"shasum": "fb738703ae8d2f9fe900c33836ddebee8b97f23e", "size": 12808, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.1.3.tgz", "integrity": "sha512-Auw9a4AxqWpa9GUfj370BMPzzyncfBABW8Mab7BGWBYDj4Isgq+cDKtx0i6u9jcX9pQDnswsaaOTgTmA5pEjuQ=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.1.3_1587545303123_0.40284197395726373"}, "_hasShrinkwrap": false, "publish_time": 1587545303251, "_cnpm_publish_time": 1587545303251, "_cnpmcore_publish_time": "2021-12-13T10:54:55.701Z", "hasInstallScript": true}, "1.2.12": {"name": "fsevents", "version": "1.2.12", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">= 4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "92e40aa2a5a5bec30cf32491587b2d31ff31072c", "_id": "fsevents@1.2.12", "_nodeVersion": "13.11.0", "_npmVersion": "6.13.7", "dist": {"shasum": "db7e0d8ec3b0b45724fd4d83d43554a8f1f0de5c", "size": 638911, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.12.tgz", "integrity": "sha512-Ggd/Ktt7E7I8pxZRbGIs7vwqAPscSESMrCSkx2FtWeqmheJgCo2R74fTsZFCifr0VTPwqRpPv17+6b8Zp7th0Q=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.12_1584609297977_0.3619495003114137"}, "_hasShrinkwrap": false, "publish_time": 1584609298168, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "_cnpm_publish_time": 1584609298168, "_cnpmcore_publish_time": "2021-12-13T10:54:56.543Z", "hasInstallScript": true}, "1.2.11": {"name": "fsevents", "version": "1.2.11", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.2.10": {"name": "fsevents", "version": "1.2.10", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.10, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "2.1.2": {"name": "fsevents", "version": "2.1.2", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "gypfile": true, "gitHead": "9cdfb441a818ad3ba18b0197a0799a0e9edec49f", "_id": "fsevents@2.1.2", "_nodeVersion": "12.12.0", "_npmVersion": "6.13.0", "dist": {"shasum": "4c0a1fb34bc68e543b4b82a9ec392bfbda840805", "size": 12777, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.1.2.tgz", "integrity": "sha512-R4wDiBwZ0KzpgOWetKDug1FZcYhqYnUYKtfZYt4mD5SBz76q0KR4Q9o7GIPamsVPGmW3EYPPJ0dOOjvx32ldZA=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.1.2_1573319310398_0.5194690646581279"}, "_hasShrinkwrap": false, "publish_time": 1573319310550, "_cnpm_publish_time": 1573319310550, "_cnpmcore_publish_time": "2021-12-13T10:54:58.552Z", "hasInstallScript": true}, "2.1.1": {"name": "fsevents", "version": "2.1.1", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "gypfile": true, "gitHead": "b0bb4d6ce4246a1805a2f19231b087fab845fbb9", "_id": "fsevents@2.1.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.11.3", "dist": {"shasum": "74c64e21df71721845d0c44fe54b7f56b82995a9", "size": 12114, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.1.1.tgz", "integrity": "sha512-4FRPXWETxtigtJW/gxzEDsX1LVbPAM93VleB83kZB+ellqbHMkyt2aJfuzNLRvFPnGi6bcE5SvfxgbXPeKteJw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.1.1_1571012081353_0.37428718989777665"}, "_hasShrinkwrap": false, "publish_time": 1571012081467, "_cnpm_publish_time": 1571012081467, "_cnpmcore_publish_time": "2021-12-13T10:54:59.000Z", "hasInstallScript": true}, "2.1.0": {"name": "fsevents", "version": "2.1.0", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "gypfile": true, "gitHead": "9f131183000dcd233b2b2be317636b95d61a10b2", "_id": "fsevents@2.1.0", "_nodeVersion": "12.10.0", "_npmVersion": "6.11.3", "dist": {"shasum": "ce1a5f9ac71c6d75278b0c5bd236d7dfece4cbaa", "size": 12372, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.1.0.tgz", "integrity": "sha512-+iXhW3LuDQsno8dOIrCIT/CBjeBWuP7PXe8w9shnj9Lebny/Gx1ZjVBYwexLz36Ri2jKuXMNpV6CYNh8lHHgrQ=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.1.0_1569891129713_0.10588534491263424"}, "_hasShrinkwrap": false, "publish_time": 1569891129822, "_cnpm_publish_time": 1569891129822, "_cnpmcore_publish_time": "2021-12-13T10:54:59.466Z", "hasInstallScript": true}, "2.0.7": {"name": "fsevents", "version": "2.0.7", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {}, "gypfile": true, "gitHead": "d93e26658a6af01d44dbb7fa8dd478d7e899f725", "_id": "fsevents@2.0.7", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"shasum": "382c9b443c6cbac4c57187cdda23aa3bf1ccfc2a", "size": 10195, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.7.tgz", "integrity": "sha512-a7YT0SV3RB+DjYcppwVDLtn13UQnmg0SWZS7ezZD0UjnLwXmy8Zm21GMVGLaFGimIqcvyMQaOJBrop8MyOp1kQ=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.7_1558012034977_0.34845496801853293"}, "_hasShrinkwrap": false, "publish_time": 1558012035212, "_cnpm_publish_time": 1558012035212, "_cnpmcore_publish_time": "2021-12-13T10:54:59.903Z", "hasInstallScript": true}, "1.2.9": {"name": "fsevents", "version": "1.2.9", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.9, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "2.0.6": {"name": "fsevents", "version": "2.0.6", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {}, "gypfile": true, "gitHead": "8f71b38402716f9cbd7ba14968514cef50915f27", "_id": "fsevents@2.0.6", "_nodeVersion": "11.14.0", "_npmVersion": "6.7.0", "dist": {"shasum": "87b19df0bfb4a1a51d7ddb51b01b5f3bedb40c33", "size": 10470, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.6.tgz", "integrity": "sha512-vfmKZp3XPM36DNF0qhW+Cdxk7xm7gTEHY1clv1Xq1arwRQuKZgAhw+NZNWbJBtuaNxzNXwhfdPYRrvIbjfS33A=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.6_1555442371447_0.5719706998892857"}, "_hasShrinkwrap": false, "publish_time": 1555442371595, "_cnpm_publish_time": 1555442371595, "_cnpmcore_publish_time": "2021-12-13T10:55:01.037Z", "hasInstallScript": true}, "2.0.5": {"name": "fsevents", "version": "2.0.5", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepublishOnly", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {}, "readmeFilename": "README.md", "gitHead": "6e16b1a4589c422d98aa084fb450196cfefc2ed5", "_id": "fsevents@2.0.5", "_nodeVersion": "11.14.0", "_npmVersion": "6.7.0", "dist": {"shasum": "d6760479b5b44391550599206f4589db5ab606d1", "size": 27221, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.5.tgz", "integrity": "sha512-MfLd8JqA1lbfD0um5vkoRwGu1uSU9aAxNCliQF5R7AmYDDSi53GQJPAuXzhGrrq8yju61XQlm0IPlANMzWH6gQ=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.5_1555433424663_0.05713479282296485"}, "_hasShrinkwrap": false, "publish_time": 1555433424835, "_cnpm_publish_time": 1555433424835, "_cnpmcore_publish_time": "2021-12-13T10:55:01.563Z", "hasInstallScript": true}, "1.2.8": {"name": "fsevents", "version": "1.2.8", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.8, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "2.0.4": {"name": "fsevents", "version": "2.0.4", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepublishOnly", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {}, "gitHead": "06dc1661cba79137dd674b92d7305660aac65682", "_id": "fsevents@2.0.4", "_nodeVersion": "11.14.0", "_npmVersion": "6.7.0", "dist": {"shasum": "44bc59e832b8570b617d7617236d739a51dca6db", "size": 16106, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.4.tgz", "integrity": "sha512-s5Nz+S1I1krznEMeI8msYq+AYL3aj6rmbK6uaMykZvSpN9zY1Ev/11O1RXbrlpJyxvn0VzhySs8LNkxwOHMl1g=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.4_1555427948499_0.9017960357016135"}, "_hasShrinkwrap": false, "publish_time": 1555427948646, "_cnpm_publish_time": 1555427948646, "_cnpmcore_publish_time": "2021-12-13T10:55:02.884Z", "hasInstallScript": true}, "2.0.3": {"name": "fsevents", "version": "2.0.3", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepublishOnly", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {}, "readmeFilename": "README.md", "gitHead": "e6d9e603d76422656570615ffa27728bee8f2fe0", "_id": "fsevents@2.0.3", "_nodeVersion": "11.13.0", "_npmVersion": "6.7.0", "dist": {"shasum": "49ffb097c132987fc84938f5e97c3a6632e04c13", "size": 15894, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.3.tgz", "integrity": "sha512-kbyPVQrgcnetW389rufw+WAl9szz0ougHcW6faFZ/RDESUwgmCWnzmXHONmdKSWiadtj6VaaewwhyX7ckL+itw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.3_1554126663037_0.09363294808882094"}, "_hasShrinkwrap": false, "publish_time": 1554126663216, "_cnpm_publish_time": 1554126663216, "_cnpmcore_publish_time": "2021-12-13T10:55:03.439Z", "hasInstallScript": true}, "1.2.7": {"name": "fsevents", "version": "1.2.7", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.7, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.2.6": {"name": "fsevents", "version": "1.2.6", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.6, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "2.0.2-pre-1": {"name": "fsevents", "version": "2.0.2-pre-1", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": "^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepare", "test": "/bin/bash ./test.sh 2>/dev/null", "prepare": "node-gyp rebuild && node-gyp clean"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {}, "readmeFilename": "Readme.md", "gitHead": "b47d7926a1f9f71267b2b0e293e70151b1ce0e57", "_id": "fsevents@2.0.2-pre-1", "_npmVersion": "6.4.1", "_nodeVersion": "11.0.0", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "dist": {"shasum": "edcd17077801eb2df0e8d15f85d0c9147e48434b", "size": 16011, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.2-pre-1.tgz", "integrity": "sha512-rcrAK4uZLlxZLgz+s8nJtLOlpgM3gsDvOTM5mjLbaSUZmiR75JCxdeqNB8NnekRMl+0sAXWShKQGHKIkyYzY7g=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.2-pre-1_1546081023950_0.5562314985010346"}, "_hasShrinkwrap": false, "publish_time": 1546081024076, "_cnpm_publish_time": 1546081024076, "_cnpmcore_publish_time": "2021-12-13T10:55:05.435Z", "hasInstallScript": true}, "2.0.1": {"name": "fsevents", "version": "2.0.1", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": ">=6.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepare", "test": "tap ./test", "prepare": "node-gyp rebuild && node-gyp clean"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~12.0.1"}, "gitHead": "9de56aca5c8f6f3c54aac68cc52be4714d42d3fa", "_id": "fsevents@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.0.0", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "dist": {"shasum": "cdc8dc0145fa4198f4500ec09df35e79e5e52cf7", "size": 13514, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.1.tgz", "integrity": "sha512-p+CXqK/iLvDESUWdn3NA3JVO9HxdfI+iXx8xR3DqWgKZvQNiEVpAyUQo0lmwz8rqksb4xaGerG291xuwwhX2kA=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.1_1541859927620_0.12146904125108637"}, "_hasShrinkwrap": false, "publish_time": 1541859927802, "_cnpm_publish_time": 1541859927802, "_cnpmcore_publish_time": "2021-12-13T10:55:06.060Z", "hasInstallScript": true}, "2.0.0": {"name": "fsevents", "version": "2.0.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "os": ["darwin"], "engines": {"node": ">=6.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "install": "[ -f fsevents.node ] || npm run prepare", "test": "tap ./test", "prepare": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~12.0.1"}, "gitHead": "934281fdaeedfd55fc4db6c3779ce68005d4621c", "_id": "fsevents@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.0.0", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "dist": {"shasum": "f3706a11382bb62d9dc21c149edcdc1975b4c6ca", "size": 13502, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.0.0.tgz", "integrity": "sha512-9eEzI5C/L25/OgIyx9jg2yQ/BhJxWd2t4EdC9mZq0VM2zdL30fyUteYqfPajZVr+ScyYxluWsFGQEzPHhf1KeA=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.0.0_1541857529243_0.10684631101034125"}, "_hasShrinkwrap": false, "publish_time": 1541857529438, "_cnpm_publish_time": 1541857529438, "_cnpmcore_publish_time": "2021-12-13T10:55:06.679Z", "hasInstallScript": true}, "1.2.4": {"name": "fsevents", "version": "1.2.4", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.4, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.2.3": {"name": "fsevents", "version": "1.2.3", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.3, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.2.2": {"name": "fsevents", "version": "1.2.2", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.2, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.2.0": {"name": "fsevents", "version": "1.2.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.2.0, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.1.3": {"name": "fsevents", "version": "1.1.3", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.1.3, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.1.2": {"name": "fsevents", "version": "1.1.2", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.1.2, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.1.1": {"name": "fsevents", "version": "1.1.1", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.1.1, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.1.0": {"name": "fsevents", "version": "1.1.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.1.0, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.18-0": {"name": "fsevents", "version": "1.0.18-0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "^2.3.0", "node-pre-gyp": "^0.6.29"}, "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node install", "prepublish": "if [ $(npm -v | head -c 1) -lt 3 ]; then exit 1; fi && npm dedupe", "test": "tap ./test"}, "binary": {"module_name": "fse", "module_path": "./lib/binding/{configuration}/{node_abi}-{platform}-{arch}/", "remote_path": "./v{version}/", "package_name": "{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz", "host": "https://fsevents-binaries.s3-us-west-2.amazonaws.com"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "960998f9296871d243d7509b219852e393a2d247", "bundleDependencies": ["node-pre-gyp"], "_id": "fsevents@1.0.18-0", "_shasum": "76b622b6aa11ad4aa645b556ad0826d8751e43ce", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "dist": {"shasum": "76b622b6aa11ad4aa645b556ad0826d8751e43ce", "size": 1252647, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.0.18-0.tgz", "integrity": "sha512-mV7IWIppONQ9O7FoFRpn8qirKOQ5wxDh+h6jlH1gDcIAQZNqf5EEdr4Vytxgn2hM/rz/vreMNeySWlZtGqkfsg=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/fsevents-1.0.18-0.tgz_1487523345336_0.20078036282211542"}, "directories": {}, "publish_time": 1487523347760, "_hasShrinkwrap": false, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "_cnpm_publish_time": 1487523347760, "_cnpmcore_publish_time": "2021-12-13T10:55:14.150Z", "hasInstallScript": true}, "1.0.17": {"name": "fsevents", "version": "1.0.17", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.17, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.15": {"name": "fsevents", "version": "1.0.15", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.15, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.14": {"name": "fsevents", "version": "1.0.14", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.14, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.12": {"name": "fsevents", "version": "1.0.12", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.12, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.11": {"name": "fsevents", "version": "1.0.11", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.11, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.10": {"name": "fsevents", "version": "1.0.10", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.10, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.9": {"name": "fsevents", "version": "1.0.9", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.9, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.8": {"name": "fsevents", "version": "1.0.8", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.8, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.7": {"name": "fsevents", "version": "1.0.7", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.7, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.6": {"name": "fsevents", "version": "1.0.6", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.6, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.5": {"name": "fsevents", "version": "1.0.5", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.5, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.4": {"name": "fsevents", "version": "1.0.4", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.4, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.3": {"name": "fsevents", "version": "1.0.3", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.3, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.2": {"name": "fsevents", "version": "1.0.2", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.2, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.1": {"name": "fsevents", "version": "1.0.1", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.1, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "1.0.0": {"name": "fsevents", "version": "1.0.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1", "node-pre-gyp": "*"}, "os": ["darwin"], "engines": {"node": ">=4.0"}, "scripts": {"test": "node ./test/fsevents.js && node ./test/function.js 2> /dev/null", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "bundleDependencies": ["node-pre-gyp"], "gypfile": true, "readmeFilename": "Readme.md", "gitHead": "909af26846834642c81d19f4148afa3b7557b058", "_id": "fsevents@1.2.11", "_nodeVersion": "13.3.0", "_npmVersion": "6.13.1", "dist": {"shasum": "67bf57f4758f02ede88fb2a1712fef4d15358be3", "size": 649719, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.11.tgz", "integrity": "sha512-+ux3lx6peh0BpvY0JebGyZoiR4D+oYzdPZMKJwkZ+sFkNJzpL7tXc/wehS49gUAxg3tmMHPHZkA8JU2rhhgDHw=="}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_1.2.11_1576322898717_0.4582099524787324"}, "_hasShrinkwrap": false, "publish_time": 1576322898959, "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues ([WARNING] Use 1.2.11 instead of 1.0.0, reason: https://github.com/advisories/GHSA-xv2f-5jw4-v95m)", "_cnpm_publish_time": 1576322898959, "_cnpmcore_publish_time": "2021-12-13T10:54:57.306Z", "hasInstallScript": true}, "0.3.8": {"name": "fsevents", "version": "0.3.8", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "^2.0.2"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "tap ./test", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gypfile": true, "gitHead": "f24d1478198676908c646b5b2b4975786dd2954b", "_id": "fsevents@0.3.8", "_shasum": "9992f1032c925c829554d0d59801dca0313a5356", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.2", "_npmUser": {"name": "bnoordhui<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "9992f1032c925c829554d0d59801dca0313a5356", "size": 7398, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.8.tgz", "integrity": "sha512-3vlmn1QaPoqSnhnorLFlp3+r3dUCZ8eZlaew+H8QhqB+0YBc9HSITh9wiZo76KYYExTC9DwG6otE/OzwbBLVIw=="}, "directories": {}, "publish_time": 1438894420505, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438894420505, "_cnpmcore_publish_time": "2021-12-13T10:55:31.127Z", "hasInstallScript": true}, "0.3.7": {"name": "fsevents", "version": "0.3.7", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "^1.8.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "tap ./test", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gypfile": true, "gitHead": "375f602e236c6f64e5ebdeb6bc39a0437a0ffef3", "_id": "fsevents@0.3.7", "_shasum": "7760c08570126c3d17a10489c0ad5ac515fbaa6e", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.2", "_npmUser": {"name": "bnoordhui<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "7760c08570126c3d17a10489c0ad5ac515fbaa6e", "size": 7351, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.7.tgz", "integrity": "sha512-NVwTC14h68lre1axfXkOQlzv86L3eMiGhaakUPWn78/wZl5OKCeAmi2Q5AehiMXahWnwccxw5kv6cYt4yPGmnQ=="}, "directories": {}, "publish_time": 1438291375082, "_hasShrinkwrap": false, "_cnpm_publish_time": 1438291375082, "_cnpmcore_publish_time": "2021-12-13T10:55:32.306Z", "hasInstallScript": true}, "0.3.6": {"name": "fsevents", "version": "0.3.6", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "^1.8.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/strongloop/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/strongloop/fsevents/issues"}, "homepage": "https://github.com/strongloop/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "c0e61c630ce3352d3c0a0c9b83e4f1481d047d3b", "_id": "fsevents@0.3.6", "_shasum": "03f029087b5388c756ef0d6707cf3656dba95c84", "_from": ".", "_npmVersion": "2.7.6", "_nodeVersion": "1.7.1", "_npmUser": {"name": "baj<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "03f029087b5388c756ef0d6707cf3656dba95c84", "size": 15805, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.6.tgz", "integrity": "sha512-rUSyAnLg0bD3fGIaq02P/HgATFbjOlD0ipIPxnnyKEIU5oja8xJN35/386eCqyUa76C5Fl47UISK7wcXQyG7RQ=="}, "directories": {}, "publish_time": 1430826056957, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430826056957, "_cnpmcore_publish_time": "2021-12-13T10:55:33.422Z", "hasInstallScript": true}, "0.3.5": {"name": "fsevents", "version": "0.3.5", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.5.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "588407e8db0e9924776d73d03c9d4acc6e1c24d0", "_id": "fsevents@0.3.5", "_shasum": "d0938147614066c0e1297647b3b8ab5a4baf4688", "_from": ".", "_npmVersion": "2.2.0", "_nodeVersion": "1.0.3", "_npmUser": {"name": "baj<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "d0938147614066c0e1297647b3b8ab5a4baf4688", "size": 15430, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.5.tgz", "integrity": "sha512-eNNT2llwCEe0ihiaeXDFbWqP1+HV/3XKqZBJBMl4NfO43M65APsntCH0QNXmW/sY4PGYSGj889f92Ce7gZgemA=="}, "directories": {}, "publish_time": 1422380036632, "_hasShrinkwrap": false, "_cnpm_publish_time": 1422380036632, "_cnpmcore_publish_time": "2021-12-13T10:55:34.372Z", "hasInstallScript": true}, "0.3.4": {"name": "fsevents", "version": "0.3.4", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.5.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "d37c595996bd5ca5a6ce55d0cb32b96319f9b317", "_id": "fsevents@0.3.4", "_shasum": "cd9769e92f803578f2ccc4a3a4da2b8556823d02", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.32", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "cd9769e92f803578f2ccc4a3a4da2b8556823d02", "size": 7181, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.4.tgz", "integrity": "sha512-r/VPd6cQeZ2wSkBo3C10D7sV4HphDUSXLr7t/bIRPD5rXoL0NUPSVum9IcVc/MEH3PWcUgkEOFMVkL2k9ZPdoA=="}, "directories": {}, "publish_time": 1421347676914, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421347676914, "_cnpmcore_publish_time": "2021-12-13T10:55:35.453Z", "hasInstallScript": true}, "0.3.3": {"name": "fsevents", "version": "0.3.3", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.5.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "d9aa70a9dda199b33ed6419557cadffeabead321", "_id": "fsevents@0.3.3", "_shasum": "db62f97d9bfb8defd4ae7718912d29fe4e766acf", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.32", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "db62f97d9bfb8defd4ae7718912d29fe4e766acf", "size": 7196, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.3.tgz", "integrity": "sha512-wMJl+do0MPvki0bnNTMUZfbAwU8k4Q8u1/q/CRQ0dycOia87BEcQabCfRghikCflMxPwjja+/2jFZ0JnTN/U7A=="}, "directories": {}, "publish_time": 1421346340249, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421346340249, "_cnpmcore_publish_time": "2021-12-13T10:55:36.490Z", "hasInstallScript": true}, "0.3.2": {"name": "fsevents", "version": "0.3.2", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.5.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "ffdaf362036b9e15bfc7924df3f5dfde5ef28d8c", "_id": "fsevents@0.3.2", "_shasum": "83b83d81b167c6e745a2f1e6d82302eedc8011ec", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.32", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "83b83d81b167c6e745a2f1e6d82302eedc8011ec", "size": 7182, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.2.tgz", "integrity": "sha512-Lw7s56CMQEFjcOlhMSiMkmp5fEMOuuy00BbtG6vbO/swMPgErGAldQI9lfZ3EmnW8O9v2ToTXHyd7wBF0dlh3g=="}, "directories": {}, "publish_time": 1421345800178, "_hasShrinkwrap": false, "_cnpm_publish_time": 1421345800178, "_cnpmcore_publish_time": "2021-12-13T10:55:37.449Z", "hasInstallScript": true}, "0.3.1": {"name": "fsevents", "version": "0.3.1", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.3.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "gitHead": "0db2eec01b94f3d900552893a59d6346ed2e37e1", "_id": "fsevents@0.3.1", "_shasum": "47d60e5c28887055d7388833ea5d54d4882c8f6f", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "47d60e5c28887055d7388833ea5d54d4882c8f6f", "size": 7117, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.1.tgz", "integrity": "sha512-K4qQsck+JTPxRMZp0YqwfjTDtxMuEdwf49WvZJcg4Uz0881HHHpDcdKSih+Y+vbd4MqEM1+rHoNF4KeLIvD4Pw=="}, "directories": {}, "publish_time": 1415145427078, "_hasShrinkwrap": false, "_cnpm_publish_time": 1415145427078, "_cnpmcore_publish_time": "2021-12-13T10:55:38.444Z", "hasInstallScript": true}, "0.3.0": {"name": "fsevents", "version": "0.3.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~1.2.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "_id": "fsevents@0.3.0", "_shasum": "90723a3d0bbab877b62d0a78db633ef2688d8a81", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "dist": {"shasum": "90723a3d0bbab877b62d0a78db633ef2688d8a81", "size": 6704, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.3.0.tgz", "integrity": "sha512-62AVhTeAUkMW+onUPTrZGCCYvhpm7RdjiVyqWVGu1+DMREjTG73Psgz4QR+ziqip6jx6DRyvg5Hu8YCWv6HG4w=="}, "directories": {}, "publish_time": 1408485657872, "_hasShrinkwrap": false, "_cnpm_publish_time": 1408485657872, "_cnpmcore_publish_time": "2021-12-13T10:55:39.578Z", "hasInstallScript": true}, "0.2.0": {"name": "fsevents", "version": "0.2.0", "description": "Native Access to Mac OS-X FSEvents", "main": "fsevents.js", "dependencies": {"nan": "~0.8.0"}, "os": ["darwin"], "engines": {"node": ">=0.8.0"}, "scripts": {"install": "node-gyp rebuild", "test": "export PATH=$PATH:`pwd`/node_nodules/.bin/ && tap ./test"}, "repository": {"type": "git", "url": "https://github.com/pipobscure/fsevents.git"}, "keywords": ["fsevents", "mac"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/pipobscure/fsevents/issues"}, "homepage": "https://github.com/pipobscure/fsevents", "devDependencies": {"tap": "~0.4.8"}, "_id": "fsevents@0.2.0", "dist": {"shasum": "1de161da042818f45bfbe11a853da8e5c6ca5d83", "size": 6526, "noattachment": false, "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.2.0.tgz", "integrity": "sha512-K08CF/omabhh26vK69KeEnqlASku1XzuF06U7vziuIJbuFmU0PnL5G4ykrhgpww3WfrfdtUNKxvVuCJ9azdF2g=="}, "_from": ".", "_npmVersion": "1.3.22", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "directories": {}, "publish_time": 1391208331014, "_hasShrinkwrap": false, "_cnpm_publish_time": 1391208331014, "_cnpmcore_publish_time": "2021-12-13T10:55:40.718Z", "hasInstallScript": true}, "0.1.6": {"name": "fsevents", "description": "Native Access to Mac OS-X FSEvents", "homepage": "https://github.com/phidelta/NodeJS-FSEvents", "version": "0.1.6", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/phidelta/fsevents"}, "licenses": [{"type": "MIT"}], "repository": {"type": "git", "url": "https://github.com/phidelta/fsevents.git"}, "main": "./fsevents.js", "engines": {"node": ">=0.8"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "readmeFilename": "Readme.md", "_id": "fsevents@0.1.6", "dist": {"tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.1.6.tgz", "shasum": "98bd0b0876548f80da4cb1069fb6c23f97a7d9a9", "size": 4749, "noattachment": false, "integrity": "sha512-dBxMCzC+wIP3KdZ5Pkr+B55IuGmTiDBPgAURAUNDGErE//QOz82o1LM4QEuGB3NJz3KVF1wwDpbVZwyiGNehUA=="}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "publish_time": 1385636513138, "_hasShrinkwrap": false, "_cnpm_publish_time": 1385636513138, "_cnpmcore_publish_time": "2021-12-13T10:55:41.669Z", "hasInstallScript": true}, "0.1.5": {"name": "fsevents", "description": "Native Access to Mac OS-X FSEvents", "homepage": "https://github.com/phidelta/NodeJS-FSEvents", "version": "0.1.5", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/phidelta/fsevents"}, "licenses": [{"type": "MIT"}], "repository": {"type": "git", "url": "https://github.com/phidelta/fsevents.git"}, "main": "./fsevents.js", "engines": {"node": ">=0.8"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "readmeFilename": "Readme.md", "_id": "fsevents@0.1.5", "dist": {"tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.1.5.tgz", "shasum": "78d27ffb17ef96dcc66e8c2151e91416c31f7eff", "size": 4696, "noattachment": false, "integrity": "sha512-9/iC5coac3gh5mqb4T6fjAQPnICDRw3+s7uDTK1vRC8UXBkM2Q5H90Thj+mu8zbb9g3H+gPSEqBtplzLRFfMKQ=="}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "phi<PERSON>ta", "email": "<EMAIL>"}, "directories": {}, "publish_time": 1373288339106, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373288339106, "_cnpmcore_publish_time": "2021-12-13T10:55:42.780Z", "hasInstallScript": true}, "0.1.4": {"name": "fsevents", "description": "Native Access to Mac OS-X FSEvents", "homepage": "https://github.com/phidelta/NodeJS-FSEvents", "version": "0.1.4", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/phidelta/NodeJS-FSEvents"}, "licenses": [{"type": "MIT"}], "repository": {"type": "git", "url": "https://github.com/phidelta/NodeJS-FSEvents.git"}, "main": "./fsevents.js", "engines": {"node": ">=0.8"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "readmeFilename": "Readme.md", "_id": "fsevents@0.1.4", "dist": {"tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.1.4.tgz", "shasum": "22b9b084d51962414f4d1cc473217f6d3943989d", "size": 4720, "noattachment": false, "integrity": "sha512-vxMQ94UxYAZn8lwnrTe8t0g5Ngens7dBGtAYEOK5BwEhWjQeoa9Ybvq6WU7Oigr4f0SlXMIWVPcZ2t6V2Rw34w=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "phi<PERSON>ta", "email": "<EMAIL>"}, "directories": {}, "publish_time": 1373150827832, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373150827832, "_cnpmcore_publish_time": "2021-12-13T10:55:43.985Z", "hasInstallScript": true}, "0.1.3": {"name": "fsevents", "description": "Native Access to Mac OS-X FSEvents", "homepage": "https://github.com/phidelta/NodeJS-FSEvents", "version": "0.1.3", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/phidelta/NodeJS-FSEvents"}, "licenses": [{"type": "MIT"}], "repository": {"type": "git", "url": "https://github.com/phidelta/NodeJS-FSEvents.git"}, "main": "./fsevents.js", "engines": {"node": ">=0.8"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "readmeFilename": "Readme.md", "_id": "fsevents@0.1.3", "dist": {"tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.1.3.tgz", "shasum": "1def205d663e595764d38a879040e01edef1084d", "size": 4667, "noattachment": false, "integrity": "sha512-HZH3ZnWzd5MEjCgWYUhDDguHZKg/B4VvVdKn0YFA2DW6b7Di1Z8Cu7SiiHJ64DcQ2JuvHygpVVELH2XDmX6VGA=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "phi<PERSON>ta", "email": "<EMAIL>"}, "directories": {}, "publish_time": 1373149595459, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373149595459, "_cnpmcore_publish_time": "2021-12-13T10:55:45.110Z", "hasInstallScript": true}, "0.1.1": {"name": "fsevents", "description": "Native Access to Mac OS-X FSEvents", "homepage": "https://github.com/phidelta/NodeJS-FSEvents", "version": "0.1.1", "maintainers": [{"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipobscure", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/phidelta/NodeJS-FSEvents"}, "licenses": [{"type": "MIT"}], "repositories": [{"type": "git", "url": "https://github.com/phidelta/NodeJS-FSEvents.git"}], "main": "./fsevents.js", "engines": {"node": ">=0.8"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "readmeFilename": "README.md", "repository": "[Circular]", "_id": "fsevents@0.1.1", "dist": {"tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-0.1.1.tgz", "shasum": "89032d234a07a6f8b280bf825409c400d6b73858", "size": 4224, "noattachment": false, "integrity": "sha512-BAEF5TUCDaNr3wEkwl3BRE/P0NbtWJvxdIq38Kk33rHsCxRmfAKFOetqVq9hlluaUV/tXo581bpudNRnPopvGw=="}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "phi<PERSON>ta", "email": "<EMAIL>"}, "directories": {}, "publish_time": 1373148242366, "_hasShrinkwrap": false, "_cnpm_publish_time": 1373148242366, "_cnpmcore_publish_time": "2021-12-13T10:55:46.264Z", "hasInstallScript": true}, "2.3.3": {"name": "fsevents", "version": "2.3.3", "description": "Native Access to MacOS FSEvents", "main": "fsevents.js", "types": "fsevents.d.ts", "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "scripts": {"clean": "node-gyp clean && rm -f fsevents.node", "build": "node-gyp clean && rm -f fsevents.node && node-gyp rebuild && node-gyp clean", "test": "/bin/bash ./test.sh 2>/dev/null", "prepublishOnly": "npm run build", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "keywords": ["fsevents", "mac"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "license": "MIT", "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "homepage": "https://github.com/fsevents/fsevents", "devDependencies": {"node-gyp": "^9.4.0"}, "gypfile": true, "gitHead": "2db891e51aa0f2975c5eaaf6aa30f13d720a830a", "_id": "fsevents@2.3.3", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "shasum": "cac6407785d03675a2a5e1a5305c697b347d90d6", "tarball": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz", "fileCount": 6, "unpackedSize": 173224, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqKmzRJwFJcSLXc/SdNTdFsUQvsS/tX+pFHr2JkniDbAiEAiTjoxV3W2IK2g8Udbes/ZP46+TtL40PX3IX/bxosfWs="}], "size": 22808}, "_npmUser": {"name": "pipobscure", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "pipobscure", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fsevents_2.3.3_1692635062642_0.01197687980904627"}, "_hasShrinkwrap": false, "_cnpmcore_publish_time": "2023-08-21T16:24:22.854Z", "publish_time": 1692635062854, "_source_registry_name": "default", "hasInstallScript": true}}, "bugs": {"url": "https://github.com/fsevents/fsevents/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://paulmillr.com"}], "homepage": "https://github.com/fsevents/fsevents", "keywords": ["fsevents", "mac"], "repository": {"type": "git", "url": "git+https://github.com/fsevents/fsevents.git"}, "_source_registry_name": "default"}