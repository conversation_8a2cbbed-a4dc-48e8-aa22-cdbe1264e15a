0 verbose cli D:\nodejs(cursor_mcp)\node.exe D:\nodejs(cursor_mcp)\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:D:\nodejs(cursor_mcp)\node_modules\npm\npmrc
4 silly config load:file:C:\Users\<USER>\Desktop\32f407\k230+servo\servo\servo\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm exec playwright install
8 verbose argv "exec" "--" "playwright" "install"
9 verbose logfile logs-max:10 dir:C:\Users\<USER>\Desktop\32f407\k230+servo\servo\servo\clean\_logs\2025-06-21T13_58_04_607Z-
10 verbose logfile C:\Users\<USER>\Desktop\32f407\k230+servo\servo\servo\clean\_logs\2025-06-21T13_58_04_607Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 http fetch GET 200 https://registry.npmmirror.com/npm 254ms
14 http fetch GET 200 https://registry.npmmirror.com/playwright 712ms (cache miss)
15 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
16 verbose Could not read global path C:\Users\<USER>\AppData\Roaming\npm, ignoring
17 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
18 silly idealTree buildDeps
19 silly fetch manifest playwright@1.53.1
20 silly packumentCache full:https://registry.npmmirror.com/playwright cache-miss
21 http fetch GET 200 https://registry.npmmirror.com/playwright 915ms (cache miss)
22 silly packumentCache full:https://registry.npmmirror.com/playwright set size:2031890 disposed:false
23 silly placeDep ROOT playwright@1.53.1 OK for:  want: 1.53.1
24 silly fetch manifest playwright-core@1.53.1
25 silly packumentCache full:https://registry.npmmirror.com/playwright-core cache-miss
26 silly fetch manifest fsevents@2.3.2
27 silly packumentCache full:https://registry.npmmirror.com/fsevents cache-miss
28 http fetch GET 200 https://registry.npmmirror.com/fsevents 24ms (cache miss)
29 silly packumentCache full:https://registry.npmmirror.com/fsevents set size:16343 disposed:false
30 http fetch GET 200 https://registry.npmmirror.com/playwright-core 1016ms (cache miss)
31 silly packumentCache full:https://registry.npmmirror.com/playwright-core set size:2096251 disposed:false
32 silly placeDep ROOT fsevents@2.3.2 OK for: playwright@1.53.1 want: 2.3.2
33 silly placeDep ROOT playwright-core@1.53.1 OK for: playwright@1.53.1 want: 1.53.1
34 silly reify moves {}
35 silly audit bulk request {
35 silly audit   playwright: [ '1.53.1' ],
35 silly audit   fsevents: [ '2.3.2' ],
35 silly audit   'playwright-core': [ '1.53.1' ]
35 silly audit }
36 verbose reify failed optional dependency C:\Users\<USER>\Desktop\32f407\k230+servo\servo\servo\clean\_npx\e41f203b7505f1fb\node_modules\fsevents
37 silly reify mark deleted [
37 silly reify   'C:\\Users\\<USER>\\Desktop\\32f407\\k230+servo\\servo\\servo\\clean\\_npx\\e41f203b7505f1fb\\node_modules\\fsevents'
37 silly reify ]
38 http cache playwright-core@https://registry.npmmirror.com/playwright-core/-/playwright-core-1.53.1.tgz 0ms (cache hit)
39 http cache playwright@https://registry.npmmirror.com/playwright/-/playwright-1.53.1.tgz 0ms (cache hit)
40 silly tarball no local data for playwright-core@https://registry.npmmirror.com/playwright-core/-/playwright-core-1.53.1.tgz. Extracting by manifest.
41 silly tarball no local data for playwright@https://registry.npmmirror.com/playwright/-/playwright-1.53.1.tgz. Extracting by manifest.
42 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/advisories/bulk 13ms
43 silly audit bulk request failed [object Object]
44 http fetch POST 404 https://registry.npmmirror.com/-/npm/v1/security/audits/quick 7ms
45 verbose audit error HttpErrorGeneral: 404 Not Found - POST https://registry.npmmirror.com/-/npm/v1/security/audits/quick - [NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet
45 verbose audit error     at D:\nodejs(cursor_mcp)\node_modules\npm\node_modules\npm-registry-fetch\lib\check-response.js:103:15
45 verbose audit error     at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
45 verbose audit error     at async [getReport] (D:\nodejs(cursor_mcp)\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:336:21)
45 verbose audit error     at async AuditReport.run (D:\nodejs(cursor_mcp)\node_modules\npm\node_modules\@npmcli\arborist\lib\audit-report.js:106:19) {
45 verbose audit error   headers: [Object: null prototype] {
45 verbose audit error     server: [ 'Tengine' ],
45 verbose audit error     date: [ 'Sat, 21 Jun 2025 13:58:18 GMT' ],
45 verbose audit error     'content-type': [ 'application/json' ],
45 verbose audit error     'transfer-encoding': [ 'chunked' ],
45 verbose audit error     connection: [ 'keep-alive' ],
45 verbose audit error     'strict-transport-security': [ 'max-age=5184000' ],
45 verbose audit error     via: [ 'kunlun1.cn8360[,404666]' ],
45 verbose audit error     'timing-allow-origin': [ '*' ],
45 verbose audit error     eagleid: [ 'ab2ba99517505142980781419e' ],
45 verbose audit error     'x-fetch-attempts': [ '1' ]
45 verbose audit error   },
45 verbose audit error   statusCode: 404,
45 verbose audit error   code: 'E404',
45 verbose audit error   method: 'POST',
45 verbose audit error   uri: 'https://registry.npmmirror.com/-/npm/v1/security/audits/quick',
45 verbose audit error   body: {
45 verbose audit error     error: '[NOT_IMPLEMENTED] /-/npm/v1/security/* not implemented yet'
45 verbose audit error   },
45 verbose audit error   pkgid: undefined
45 verbose audit error }
46 silly audit error [object Object]
47 silly audit report null
48 http fetch GET 200 https://cdn.npmmirror.com/packages/playwright/1.53.1/playwright-1.53.1.tgz 987ms (cache miss)
49 http fetch GET 200 https://cdn.npmmirror.com/packages/playwright-core/1.53.1/playwright-core-1.53.1.tgz 1231ms (cache miss)
50 verbose cwd C:\Users\<USER>\Desktop\32f407\k230+servo\servo\servo
51 verbose os Windows_NT 10.0.26100
52 verbose node v22.16.0
53 verbose npm  v10.9.2
54 notice
54 notice New [31mmajor[39m version of npm available! [31m10.9.2[39m -> [34m11.4.2[39m
54 notice Changelog: [34mhttps://github.com/npm/cli/releases/tag/v11.4.2[39m
54 notice To update run: [4mnpm install -g npm@11.4.2[24m
54 notice  { force: true, [Symbol(proc-log.meta)]: true }
55 verbose exit 0
56 info ok
